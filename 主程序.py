import os
import socket
import subprocess
import platform
from flask import Flask, render_template, jsonify, send_file, abort
from pathlib import Path
import mimetypes
import urllib.parse

app = Flask(__name__)

# 配置图片路径
IMAGE_BASE_PATH = r"C:\Users\<USER>\Desktop\Python代码项目\===闲鱼做图项目 - 重构\游戏商品图\单机游戏"

def find_available_port(start_port=5000):
    """智能寻找可用端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def is_image_file(filename):
    """检查文件是否为图片"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
    return Path(filename).suffix.lower() in image_extensions

def get_folder_images():
    """获取所有文件夹及其图片"""
    folders_data = []
    
    if not os.path.exists(IMAGE_BASE_PATH):
        return folders_data
    
    try:
        # 遍历所有子文件夹
        for folder_name in sorted(os.listdir(IMAGE_BASE_PATH)):
            folder_path = os.path.join(IMAGE_BASE_PATH, folder_name)
            
            if os.path.isdir(folder_path):
                images = []
                
                # 获取文件夹中的所有图片
                try:
                    for file_name in sorted(os.listdir(folder_path)):
                        file_path = os.path.join(folder_path, file_name)
                        
                        if os.path.isfile(file_path) and is_image_file(file_name):
                            images.append({
                                'name': file_name,
                                'path': file_path,
                                'relative_path': os.path.join(folder_name, file_name),
                                'url_path': f"/image/{urllib.parse.quote(folder_name)}/{urllib.parse.quote(file_name)}"
                            })
                except PermissionError:
                    print(f"无法访问文件夹: {folder_path}")
                    continue
                
                if images:  # 只添加包含图片的文件夹
                    folders_data.append({
                        'folder_name': folder_name,
                        'folder_path': folder_path,
                        'images': images,
                        'image_count': len(images)
                    })
    
    except Exception as e:
        print(f"读取文件夹时出错: {e}")
    
    return folders_data

@app.route('/')
def index():
    """主页面"""
    folders_data = get_folder_images()
    return render_template('主页.html', folders_data=folders_data, base_path=IMAGE_BASE_PATH)

@app.route('/api/folders')
def api_folders():
    """API接口获取文件夹数据"""
    folders_data = get_folder_images()
    return jsonify(folders_data)

@app.route('/image/<path:folder_name>/<path:filename>')
def serve_image(folder_name, filename):
    """提供图片文件服务"""
    try:
        # URL解码
        folder_name = urllib.parse.unquote(folder_name)
        filename = urllib.parse.unquote(filename)

        # 构建完整路径
        file_path = os.path.join(IMAGE_BASE_PATH, folder_name, filename)

        # 安全检查：确保文件在指定目录内
        if not file_path.startswith(IMAGE_BASE_PATH):
            abort(403)

        # 检查文件是否存在且是图片
        if os.path.exists(file_path) and is_image_file(filename):
            return send_file(file_path)
        else:
            abort(404)

    except Exception as e:
        print(f"图片服务错误: {e}")
        abort(500)

@app.route('/open_folder/<path:folder_path>')
def open_folder(folder_path):
    """打开文件夹"""
    try:
        full_path = os.path.join(IMAGE_BASE_PATH, folder_path)
        
        if os.path.exists(full_path):
            if platform.system() == 'Windows':
                subprocess.run(['explorer', full_path], check=True)
                return jsonify({'status': 'success', 'message': f'已打开文件夹: {folder_path}'})
            else:
                return jsonify({'status': 'error', 'message': '当前系统不支持自动打开文件夹'})
        else:
            return jsonify({'status': 'error', 'message': '文件夹不存在'})
    
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'打开文件夹失败: {str(e)}'})

if __name__ == '__main__':
    # 智能寻找可用端口
    port = find_available_port()
    
    if port:
        print(f"启动服务器，端口: {port}")
        print(f"访问地址: http://localhost:{port}")
        print(f"图片路径: {IMAGE_BASE_PATH}")
        
        # 检查路径是否存在
        if not os.path.exists(IMAGE_BASE_PATH):
            print(f"警告: 图片路径不存在 - {IMAGE_BASE_PATH}")
        
        app.run(host='0.0.0.0', port=port, debug=True)
    else:
        print("无法找到可用端口")
