闲鱼图片审核系统 - 使用说明
=====================================

功能特点：
---------
✓ 智能端口检测，自动寻找可用端口启动
✓ 大屏显示，专为电脑浏览器优化
✓ 一个文件夹一行，图片横向排列
✓ 点击图片在浏览器新标签页中放大显示
✓ 文件夹操作按钮：打开文件夹、删除文件夹
✓ 自动忽略指定图片：商品说明与承诺.png、游戏介绍图.png、游戏配置图.png
✓ 实时统计文件夹和图片数量

使用方法：
---------
1. 双击运行 "启动.bat" 文件
2. 在浏览器中访问显示的地址（如：http://localhost:5000）
3. 点击图片可在新标签页中放大查看
4. 点击"打开文件夹"按钮可在资源管理器中打开对应文件夹
5. 点击"删除文件夹"按钮可删除整个文件夹（需确认）

系统要求：
---------
- Windows 10 系统
- Python 3.7 或更高版本
- Flask库（系统会自动提示安装）
- 现代浏览器（Chrome、Edge、Firefox等）

配置说明：
---------
默认扫描路径：C:\Users\<USER>\Desktop\Python代码项目\===闲鱼做图项目 - 重构\游戏商品图\单机游戏

如需修改扫描路径，请编辑 "主程序.py" 文件中的 IMAGE_BASE_PATH 变量。

支持的图片格式：
--------------
.jpg, .jpeg, .png, .gif, .bmp, .webp, .tiff, .svg

注意事项：
---------
- 系统会自动跳过没有图片的文件夹
- 删除文件夹操作不可恢复，请谨慎使用
- 建议使用Chrome或Edge浏览器以获得最佳体验
- 图片点击后会在新标签页中打开，方便查看细节
