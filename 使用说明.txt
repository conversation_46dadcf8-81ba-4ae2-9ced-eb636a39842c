闲鱼图片审核系统 - 使用说明
=====================================

功能特点：
---------
✓ 智能端口检测，自动寻找可用端口启动
✓ 大屏显示，专为电脑浏览器优化
✓ 一个文件夹一行，图片横向排列
✓ 点击任意图片或文件夹标题可快速打开对应文件夹
✓ 悬停显示图片文件名
✓ 实时统计文件夹和图片数量

使用方法：
---------
1. 双击运行 "启动.bat" 文件
2. 等待系统自动安装依赖并启动服务器
3. 浏览器会显示访问地址（如：http://localhost:5000）
4. 在浏览器中打开该地址即可使用

系统要求：
---------
- Windows 10 系统
- Python 3.7 或更高版本
- 现代浏览器（Chrome、Edge、Firefox等）

配置说明：
---------
默认扫描路径：C:\Users\<USER>\Desktop\Python代码项目\===闲鱼做图项目 - 重构\游戏商品图\单机游戏

如需修改扫描路径，请编辑 "主程序.py" 文件中的 IMAGE_BASE_PATH 变量。

支持的图片格式：
--------------
.jpg, .jpeg, .png, .gif, .bmp, .webp, .tiff, .svg

注意事项：
---------
- 系统会自动跳过没有图片的文件夹
- 点击图片或文件夹标题会在Windows资源管理器中打开对应文件夹
- 如果图片无法显示，请检查文件路径和权限
- 建议使用Chrome或Edge浏览器以获得最佳体验

故障排除：
---------
1. 如果端口被占用，系统会自动寻找其他可用端口
2. 如果图片不显示，可能是浏览器安全策略限制，请尝试使用不同浏览器
3. 如果无法打开文件夹，请检查文件夹路径是否正确

技术支持：
---------
如有问题，请检查控制台输出的错误信息。
