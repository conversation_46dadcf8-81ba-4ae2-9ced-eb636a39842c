<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>闲鱼图片审核系统</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: #f5f5f5;
      color: #333;
      line-height: 1.4;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .stats {
      margin-top: 8px;
      font-size: 14px;
      opacity: 0.9;
    }

    .container {
      max-width: 100%;
      padding: 20px;
    }

    .folder-section {
      background: white;
      margin-bottom: 25px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.08);
      overflow: hidden;
      transition: transform 0.2s ease;
    }

    .folder-section:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .folder-header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 15px 20px;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .folder-header:hover {
      background: linear-gradient(135deg, #43a3f5 0%, #00e8f5 100%);
    }

    .folder-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .folder-info {
      font-size: 13px;
      opacity: 0.9;
    }

    .images-container {
      padding: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: flex-start;
    }

    .image-item {
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 3px 12px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
      cursor: pointer;
      background: white;
    }

    .image-item:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 20px rgba(0,0,0,0.25);
    }

    .image-item img {
      width: 200px;
      height: 150px;
      object-fit: cover;
      display: block;
      transition: opacity 0.3s ease;
    }

    .image-item:hover img {
      opacity: 0.9;
    }

    .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0,0,0,0.8));
      color: white;
      padding: 8px 10px;
      font-size: 12px;
      transform: translateY(100%);
      transition: transform 0.3s ease;
    }

    .image-item:hover .image-overlay {
      transform: translateY(0);
    }

    .loading {
      text-align: center;
      padding: 40px;
      font-size: 16px;
      color: #666;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-state h3 {
      font-size: 20px;
      margin-bottom: 10px;
    }

    .path-info {
      background: #e8f4fd;
      border: 1px solid #bee5eb;
      border-radius: 6px;
      padding: 10px 15px;
      margin-bottom: 20px;
      font-size: 13px;
      color: #0c5460;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      transform: translateX(400px);
      transition: transform 0.3s ease;
    }

    .notification.success {
      background: #28a745;
    }

    .notification.error {
      background: #dc3545;
    }

    .notification.show {
      transform: translateX(0);
    }

    @media (max-width: 1200px) {
      .image-item img {
        width: 180px;
        height: 135px;
      }
    }

    @media (max-width: 768px) {
      .image-item img {
        width: 160px;
        height: 120px;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <h1>闲鱼图片审核系统</h1>
    <div class="stats">
      共 <span id="folder-count">{{ folders_data|length }}</span> 个文件夹，
      <span id="total-images">{{ folders_data|sum(attribute='image_count') }}</span> 张图片
    </div>
  </header>

  <div class="container">
    <div class="path-info">
      <strong>扫描路径:</strong> {{ base_path }}
    </div>

    {% if folders_data %}
      {% for folder in folders_data %}
      <div class="folder-section">
        <div class="folder-header" onclick="openFolder('{{ folder.folder_name }}')">
          <div class="folder-title">{{ folder.folder_name }}</div>
          <div class="folder-info">{{ folder.image_count }} 张图片</div>
        </div>
        
        <div class="images-container">
          {% for image in folder.images %}
          <div class="image-item" onclick="openFolder('{{ folder.folder_name }}')">
            <img src="file:///{{ image.path }}" 
                 alt="{{ image.name }}" 
                 onerror="this.style.display='none'" />
            <div class="image-overlay">
              {{ image.name }}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
    {% else %}
      <div class="empty-state">
        <h3>未找到图片文件</h3>
        <p>请检查路径是否正确，或确保文件夹中包含图片文件</p>
      </div>
    {% endif %}
  </div>

  <div id="notification" class="notification"></div>

  <script>
    function openFolder(folderName) {
      fetch(`/open_folder/${encodeURIComponent(folderName)}`)
        .then(response => response.json())
        .then(data => {
          showNotification(data.message, data.status);
        })
        .catch(error => {
          showNotification('操作失败: ' + error.message, 'error');
        });
    }

    function showNotification(message, type) {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.className = `notification ${type}`;
      notification.classList.add('show');
      
      setTimeout(() => {
        notification.classList.remove('show');
      }, 3000);
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
      console.log('图片审核系统已加载');
      
      // 检查图片加载失败的情况
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        img.addEventListener('error', function() {
          this.parentElement.style.opacity = '0.5';
          this.parentElement.title = '图片加载失败: ' + this.alt;
        });
      });
    });
  </script>
</body>
</html>
